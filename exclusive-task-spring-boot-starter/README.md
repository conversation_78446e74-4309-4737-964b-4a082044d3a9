# Exclusive Task Spring Boot Starter

A Spring Boot starter that provides distributed lock functionality for exclusive task execution using Redis and Redisson.

## Features

- **Distributed Locking**: Uses Redis distributed locks to ensure exclusive execution across multiple application instances
- **AOP Integration**: Simple annotation-based approach using `@ExclusiveTask`
- **Configurable**: Extensive configuration options for lock behavior
- **Retry Mechanism**: Built-in retry logic for lock acquisition
- **Debug Support**: Optional debug logging for troubleshooting
- **Spring Boot Auto-Configuration**: Zero-configuration setup with sensible defaults

## Quick Start

### 1. Add Dependency

Add the starter to your Spring Boot project:

```xml
<dependency>
    <groupId>com.wosai.pay</groupId>
    <artifactId>exclusive-task-spring-boot-starter</artifactId>
    <version>1.1.6</version>
</dependency>
```

### 2. Configure Redis

Ensure you have Redisson configured in your application. Add Redisson dependency:

```xml
<dependency>
    <groupId>org.redisson</groupId>
    <artifactId>redisson-spring-boot-starter</artifactId>
    <version>3.24.3</version>
</dependency>
```

### 3. Use the Annotation

Annotate methods that need exclusive execution:

```java
@Service
public class PaymentService {
    
    @ExclusiveTask(taskName = "processPayment", waitTime = 10, leaseTime = 30)
    public void processPayment(String orderId) {
        // This method will be executed exclusively across all instances
        // Only one instance can execute this method at a time
    }
}
```

## Configuration

Configure the starter using application properties:

```yaml
exclusive-task:
  enabled: true                           # Enable/disable the functionality
  default-lock-key-prefix: "exclusive-task"  # Default prefix for lock keys
  default-wait-time: 0                    # Default wait time in seconds
  default-lease-time: -1                  # Default lease time in seconds (-1 = no auto-release)
  default-skip-on-lock-failure: true      # Skip execution if lock acquisition fails
  enable-debug-logging: false             # Enable debug logging
  max-retry-attempts: 0                   # Maximum retry attempts for lock acquisition
  retry-interval-ms: 100                  # Retry interval in milliseconds
  use-fair-lock: false                    # Use fair locks (FIFO ordering)
```

## Annotation Parameters

The `@ExclusiveTask` annotation supports the following parameters:

- `taskName`: Unique name for the task (required)
- `waitTime`: Maximum time to wait for lock acquisition (default: 0)
- `leaseTime`: Maximum time to hold the lock (default: -1, no auto-release)
- `timeUnit`: Time unit for waitTime and leaseTime (default: SECONDS)
- `skipOnLockFailure`: Skip execution if lock acquisition fails (default: true)
- `lockKeyPrefix`: Custom lock key prefix (optional)
- `includeParameters`: Include method parameters in lock key (default: false)
- `parameterNames`: Specific parameter names to include (optional)

## Examples

### Basic Usage

```java
@ExclusiveTask(taskName = "dailyReport")
public void generateDailyReport() {
    // Only one instance will execute this method
}
```

### With Wait Time

```java
@ExclusiveTask(taskName = "dataSync", waitTime = 30, timeUnit = TimeUnit.SECONDS)
public void syncData() {
    // Wait up to 30 seconds for lock acquisition
}
```

### Parameter-Specific Locking

```java
@ExclusiveTask(
    taskName = "userOperation", 
    includeParameters = true,
    parameterNames = {"userId"}
)
public void processUserOperation(String userId, String operation) {
    // Lock is specific to the userId parameter
    // Multiple users can be processed simultaneously
}
```

## Requirements

- Java 8+
- Spring Boot 2.x
- Redis server
- Redisson client

## License

This project is licensed under the MIT License.
