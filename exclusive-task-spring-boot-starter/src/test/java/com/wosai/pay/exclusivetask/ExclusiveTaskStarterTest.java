package com.wosai.pay.exclusivetask;

import com.wosai.pay.exclusivetask.annotation.ExclusiveTask;
import com.wosai.pay.exclusivetask.config.ExclusiveTaskAutoConfiguration;
import com.wosai.pay.exclusivetask.config.ExclusiveTaskProperties;
import com.wosai.pay.exclusivetask.interceptor.ExclusiveTaskInterceptor;
import com.wosai.pay.exclusivetask.parser.ExclusiveTaskParser;
import org.junit.jupiter.api.Test;
import org.springframework.boot.autoconfigure.AutoConfigurations;
import org.springframework.boot.test.context.runner.ApplicationContextRunner;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.redisson.api.RedissonClient;
import org.mockito.Mockito;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Test for ExclusiveTask Spring Boot Starter
 */
public class ExclusiveTaskStarterTest {

    private final ApplicationContextRunner contextRunner = new ApplicationContextRunner()
            .withConfiguration(AutoConfigurations.of(ExclusiveTaskAutoConfiguration.class))
            .withUserConfiguration(TestConfiguration.class);

    @Test
    public void testAutoConfigurationLoadsSuccessfully() {
        this.contextRunner
                .withPropertyValues("exclusive-task.enabled=true")
                .run(context -> {
                    assertThat(context).hasSingleBean(ExclusiveTaskProperties.class);
                    assertThat(context).hasSingleBean(ExclusiveTaskParser.class);
                    assertThat(context).hasSingleBean(ExclusiveTaskInterceptor.class);
                });
    }

    @Test
    public void testAutoConfigurationDisabled() {
        this.contextRunner
                .withPropertyValues("exclusive-task.enabled=false")
                .run(context -> {
                    assertThat(context).doesNotHaveBean(ExclusiveTaskInterceptor.class);
                });
    }

    @Test
    public void testPropertiesConfiguration() {
        this.contextRunner
                .withPropertyValues(
                        "exclusive-task.enabled=true",
                        "exclusive-task.default-lock-key-prefix=test-prefix",
                        "exclusive-task.default-wait-time=10",
                        "exclusive-task.enable-debug-logging=true"
                )
                .run(context -> {
                    ExclusiveTaskProperties properties = context.getBean(ExclusiveTaskProperties.class);
                    assertThat(properties.getDefaultLockKeyPrefix()).isEqualTo("test-prefix");
                    assertThat(properties.getDefaultWaitTime()).isEqualTo(10L);
                    assertThat(properties.isEnableDebugLogging()).isTrue();
                });
    }

    @Configuration
    static class TestConfiguration {
        @Bean
        public RedissonClient redissonClient() {
            return Mockito.mock(RedissonClient.class);
        }
    }

    /**
     * Test service with exclusive task annotation
     */
    static class TestService {
        @ExclusiveTask(taskName = "testTask")
        public String exclusiveMethod() {
            return "executed";
        }
    }
}
