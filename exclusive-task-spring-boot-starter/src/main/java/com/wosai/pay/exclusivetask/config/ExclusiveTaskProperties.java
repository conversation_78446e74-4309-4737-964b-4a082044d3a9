package com.wosai.pay.exclusivetask.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.concurrent.TimeUnit;

/**
 * Configuration properties for exclusive task execution
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@ConfigurationProperties(prefix = "exclusive-task")
public class ExclusiveTaskProperties {

    /**
     * Whether to enable exclusive task functionality
     */
    private boolean enabled = true;

    /**
     * Default lock key prefix for all exclusive tasks
     */
    private String defaultLockKeyPrefix = "exclusive-task";

    /**
     * Default wait time for acquiring locks (in seconds)
     */
    private long defaultWaitTime = 0L;

    /**
     * Default lease time for holding locks (in seconds)
     * -1 means no automatic release
     */
    private long defaultLeaseTime = -1L;

    /**
     * Default time unit for wait time and lease time
     */
    private TimeUnit defaultTimeUnit = TimeUnit.SECONDS;

    /**
     * Whether to skip execution by default when lock acquisition fails
     */
    private boolean defaultSkipOnLockFailure = true;

    /**
     * Whether to include method parameters in lock key by default
     */
    private boolean defaultIncludeParameters = false;

    /**
     * Whether to enable debug logging for lock operations
     */
    private boolean enableDebugLogging = false;

    /**
     * Maximum number of retry attempts for lock acquisition
     */
    private int maxRetryAttempts = 0;

    /**
     * Retry interval in milliseconds
     */
    private long retryIntervalMs = 100L;

    /**
     * Whether to use fair locks (FIFO ordering)
     */
    private boolean useFairLock = false;

    /**
     * Lock watchdog timeout in milliseconds
     * This is used by Redisson for automatic lock renewal
     */
    private long lockWatchdogTimeoutMs = 30000L;

    /**
     * Custom separator for building lock keys
     */
    private String lockKeySeparator = ":";

    /**
     * Whether to include class name in lock key
     */
    private boolean includeClassName = true;

    /**
     * Whether to include method name in lock key
     */
    private boolean includeMethodName = true;

    /**
     * Custom lock key pattern
     * Available placeholders: {prefix}, {className}, {methodName}, {taskName}, {parameters}
     */
    private String lockKeyPattern = "{prefix}{separator}{className}{separator}{methodName}{separator}{taskName}";
}

