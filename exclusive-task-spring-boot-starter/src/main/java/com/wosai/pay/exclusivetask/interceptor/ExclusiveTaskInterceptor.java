package com.wosai.pay.exclusivetask.interceptor;

import com.wosai.pay.exclusivetask.annotation.ExclusiveTask;
import com.wosai.pay.exclusivetask.config.ExclusiveTaskProperties;
import com.wosai.pay.exclusivetask.parser.ExclusiveTaskParser;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * AOP interceptor for exclusive task execution using Redis distributed locks
 *
 * <p>This interceptor automatically applies distributed locking to methods annotated
 * with {@link ExclusiveTask}. It ensures that only one instance of the application
 * can execute the annotated method at a time.</p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Component
@Aspect
@Order(10)
@RequiredArgsConstructor
@ConditionalOnProperty(prefix = "exclusive-task", name = "enabled", havingValue = "true", matchIfMissing = true)
public class ExclusiveTaskInterceptor {

    private final RedissonClient redissonClient;
    private final ExclusiveTaskParser exclusiveTaskParser;
    private final ExclusiveTaskProperties properties;

    /**
     * Pointcut for methods annotated with @ExclusiveTask
     */
    @Pointcut("@annotation(com.wosai.pay.exclusivetask.annotation.ExclusiveTask)")
    public void exclusiveTaskPoint() {}

    /**
     * Around advice for exclusive task execution
     *
     * @param joinPoint the proceeding join point
     * @return the result of method execution or null if lock acquisition failed
     * @throws Throwable if method execution throws an exception
     */
    @Around("exclusiveTaskPoint()")
    public Object invoke(ProceedingJoinPoint joinPoint) throws Throwable {
        MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
        ExclusiveTask exclusiveTask = exclusiveTaskParser.getExclusiveTask(methodSignature);

        if (exclusiveTask == null) {
            log.warn("ExclusiveTask annotation not found on method: {}", methodSignature.getMethod().getName());
            return joinPoint.proceed();
        }

        String lockKey = exclusiveTaskParser.buildLockKey(joinPoint, exclusiveTask);
        RLock lock = getLock(lockKey, exclusiveTask);

        if (properties.isEnableDebugLogging()) {
            log.debug("Attempting to acquire lock: {} for method: {}",
                    lockKey, methodSignature.getMethod().getName());
        }

        Object result = null;
        boolean lockAcquired = false;

        try {
            lockAcquired = tryAcquireLock(lock, exclusiveTask);

            if (lockAcquired) {
                if (properties.isEnableDebugLogging()) {
                    log.debug("Lock acquired successfully: {}", lockKey);
                }
                result = joinPoint.proceed();
            } else {
                handleLockAcquisitionFailure(exclusiveTask, lockKey, methodSignature);
            }

        } catch (Exception e) {
            log.error("Error executing exclusive task with lock: {}", lockKey, e);
            throw e;
        } finally {
            if (lockAcquired && lock.isHeldByCurrentThread()) {
                try {
                    lock.unlock();
                    if (properties.isEnableDebugLogging()) {
                        log.debug("Lock released: {}", lockKey);
                    }
                } catch (Exception e) {
                    log.error("Error releasing lock: {}", lockKey, e);
                }
            }
        }

        return result;
    }

    /**
     * Get appropriate lock type based on configuration
     */
    private RLock getLock(String lockKey, ExclusiveTask exclusiveTask) {
        if (properties.isUseFairLock()) {
            return redissonClient.getFairLock(lockKey);
        } else {
            return redissonClient.getLock(lockKey);
        }
    }

    /**
     * Try to acquire the lock with retry mechanism
     */
    private boolean tryAcquireLock(RLock lock, ExclusiveTask exclusiveTask) throws InterruptedException {
        long waitTime = getWaitTime(exclusiveTask);
        long leaseTime = getLeaseTime(exclusiveTask);
        TimeUnit timeUnit = getTimeUnit(exclusiveTask);

        int maxRetries = properties.getMaxRetryAttempts();
        long retryInterval = properties.getRetryIntervalMs();

        for (int attempt = 0; attempt <= maxRetries; attempt++) {
            boolean acquired;

            if (waitTime > 0) {
                if (leaseTime > 0) {
                    acquired = lock.tryLock(waitTime, leaseTime, timeUnit);
                } else {
                    acquired = lock.tryLock(waitTime, timeUnit);
                }
            } else {
                if (leaseTime > 0) {
                    acquired = lock.tryLock(0, leaseTime, timeUnit);
                } else {
                    acquired = lock.tryLock();
                }
            }

            if (acquired) {
                return true;
            }

            if (attempt < maxRetries) {
                if (properties.isEnableDebugLogging()) {
                    log.debug("Lock acquisition failed, retrying in {}ms (attempt {}/{})",
                            retryInterval, attempt + 1, maxRetries);
                }
                Thread.sleep(retryInterval);
            }
        }

        return false;
    }

    /**
     * Handle lock acquisition failure
     */
    private void handleLockAcquisitionFailure(ExclusiveTask exclusiveTask, String lockKey,
                                              MethodSignature methodSignature) {
        boolean skipOnFailure = exclusiveTask.skipOnLockFailure() || properties.isDefaultSkipOnLockFailure();

        if (skipOnFailure) {
            log.info("Lock acquisition failed for key: {}, skipping execution of method: {}",
                    lockKey, methodSignature.getMethod().getName());
        } else {
            log.warn("Lock acquisition failed for key: {}, method: {} will not be executed",
                    lockKey, methodSignature.getMethod().getName());
        }
    }

    /**
     * Get wait time from annotation or default configuration
     */
    private long getWaitTime(ExclusiveTask exclusiveTask) {
        return exclusiveTask.waitTime() >= 0 ? exclusiveTask.waitTime() : properties.getDefaultWaitTime();
    }

    /**
     * Get lease time from annotation or default configuration
     */
    private long getLeaseTime(ExclusiveTask exclusiveTask) {
        return exclusiveTask.leaseTime() >= -1 ? exclusiveTask.leaseTime() : properties.getDefaultLeaseTime();
    }

    /**
     * Get time unit from annotation or default configuration
     */
    private TimeUnit getTimeUnit(ExclusiveTask exclusiveTask) {
        return exclusiveTask.timeUnit() != null ? exclusiveTask.timeUnit() : properties.getDefaultTimeUnit();
    }
}
