package com.wosai.pay.exclusivetask.config;

import com.wosai.pay.exclusivetask.interceptor.ExclusiveTaskInterceptor;
import com.wosai.pay.exclusivetask.parser.ExclusiveTaskParser;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.EnableAspectJAutoProxy;

/**
 * Auto-configuration for exclusive task functionality
 *
 * <p>This configuration class automatically sets up the necessary beans for
 * exclusive task execution when the required dependencies are present.</p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Configuration
@EnableAspectJAutoProxy
@EnableConfigurationProperties(ExclusiveTaskProperties.class)
@ConditionalOnClass({RedissonClient.class})
@ConditionalOnProperty(prefix = "exclusive-task", name = "enabled", havingValue = "true", matchIfMissing = true)
@AutoConfigureAfter(name = "org.redisson.spring.starter.RedissonAutoConfiguration")
public class ExclusiveTaskAutoConfiguration {

    /**
     * Create ExclusiveTaskParser bean
     *
     * @param properties the exclusive task properties
     * @return ExclusiveTaskParser instance
     */
    @Bean
    @ConditionalOnMissingBean
    public ExclusiveTaskParser exclusiveTaskParser(ExclusiveTaskProperties properties) {
        log.info("Creating ExclusiveTaskParser bean");
        return new ExclusiveTaskParser(properties);
    }

    /**
     * Create ExclusiveTaskInterceptor bean
     *
     * @param redissonClient the Redisson client
     * @param exclusiveTaskParser the task parser
     * @param properties the exclusive task properties
     * @return ExclusiveTaskInterceptor instance
     */
    @Bean
    @ConditionalOnMissingBean
    @ConditionalOnBean(RedissonClient.class)
    public ExclusiveTaskInterceptor exclusiveTaskInterceptor(
            RedissonClient redissonClient,
            ExclusiveTaskParser exclusiveTaskParser,
            ExclusiveTaskProperties properties) {

        log.info("Creating ExclusiveTaskInterceptor bean with properties: {}", properties);
        return new ExclusiveTaskInterceptor(redissonClient, exclusiveTaskParser, properties);
    }

    /**
     * Configuration for development/testing environments
     */
    @Configuration
    @ConditionalOnProperty(prefix = "exclusive-task", name = "enable-debug-logging", havingValue = "true")
    static class DebugConfiguration {

        public DebugConfiguration() {
            log.info("Exclusive task debug logging is enabled");
        }
    }

    /**
     * Configuration for production environments
     */
    @Configuration
    @ConditionalOnProperty(prefix = "exclusive-task", name = "enable-debug-logging", havingValue = "false", matchIfMissing = true)
    static class ProductionConfiguration {

        public ProductionConfiguration() {
            log.info("Exclusive task is configured for production use");
        }
    }
}
