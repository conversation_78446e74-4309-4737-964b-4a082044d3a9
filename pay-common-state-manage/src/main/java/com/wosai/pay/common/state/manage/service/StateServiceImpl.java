package com.wosai.pay.common.state.manage.service;

import com.wosai.pay.common.data.Criteria;
import com.wosai.pay.common.state.manage.constant.CommonStateConstant;
import com.wosai.pay.common.state.manage.dao.CommonStateDao;
import com.wosai.pay.common.state.manage.dao.entity.CommonStateEntity;
import com.wosai.pay.common.state.manage.request.StateBaseBean;
import com.wosai.pay.common.state.manage.request.StateChangeBaseBean;
import com.wosai.pay.common.state.manage.request.StateLogParamsDto;
import com.wosai.pay.common.state.manage.result.StateDicResult;
import com.wosai.pay.common.state.manage.result.StateResult;
import com.wosai.pay.common.state.manage.service.processor.StateProcessor;
import com.wosai.pay.common.state.manage.service.processor.StateProcessorRegistry;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;


import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Logger;

/**
 * 状态服务实现类
 * 实现了状态的查询和更新逻辑，支持不同业务类型的自定义处理
 */
public class StateServiceImpl implements StateService {

    private static final Logger LOGGER = Logger.getLogger(StateServiceImpl.class.getName());

    private final CommonStateDao commonStateDao;

    public StateServiceImpl(CommonStateDao commonStateDao) {
        this.commonStateDao = commonStateDao;
    }

    /**
     * 注册一个状态处理器
     *
     * @param processor 要注册的处理器
     * @return 注册是否成功
     */
    public boolean registerProcessor(StateProcessor<?, ?> processor) {
        return StateProcessorRegistry.register(processor);
    }


    @Override
    @Transactional(isolation = Isolation.REPEATABLE_READ)
    public <T extends StateChangeBaseBean, R extends StateResult> Boolean updateState(T updateStateBaseBean, StateLogParamsDto stateLogParamsDto) {
        // 验证业务类型和状态类型
        String biz = updateStateBaseBean.getBiz();
        Integer type = updateStateBaseBean.getType();
        StateConfig.validateBusinessTypeAndSubState(biz, type);

        R oldStateResult = queryState(updateStateBaseBean);
        // 获取业务类型对应的处理器
        String opType = updateStateBaseBean.getOpType();
        StateProcessor<StateBaseBean, StateResult> processor = StateProcessorRegistry.getProcessor(opType);
        if (processor == null) {
            throw new IllegalStateException("未找到实体类型[" + opType + "]对应的处理器");
        }

        // 初始化或获取状态实体
        CommonStateEntity entity = initializeAndGetStateEntity(updateStateBaseBean, processor);
        String originalSubState = entity.getSubStatesBits();
        // 更新子状态
        Map<Integer, Boolean> subStates = entity.getSubStates();
        if (subStates == null) {
            subStates = new HashMap<>();
            entity.setSubStates(subStates);
        }
        subStates.put(type, updateStateBaseBean.getOpen());
        entity.setSubStates(subStates);
        // 更新总状态和其他信息
        entity.setRemark(updateStateBaseBean.getRemark());
        entity.setState(CommonStateConstant.DEFAULT_SUB_STATES_BITS.equals(entity.getSubStatesBits()));
        if (originalSubState.equals(entity.getSubStatesBits())) {
            return Boolean.TRUE;
        }
        // 更新状态
        commonStateDao.update(entity);

        R curStateResult = queryState(updateStateBaseBean);

        // 后置处理
        processor.afterStateUpdate(updateStateBaseBean, oldStateResult, curStateResult,stateLogParamsDto);

        return Boolean.TRUE;
    }

    @Override
    @SuppressWarnings("unchecked")
    public <T extends StateBaseBean, R extends StateResult> R queryState(T stateBaseBean) {
        String biz = stateBaseBean.getBiz();
        String opType = stateBaseBean.getOpType();
        StateConfig.validateBusinessTypeAndSubState(biz, null);
        List<StateDicResult.SubStateDic> stateDicList = StateConfig.getSubStateList(biz);

        // 获取业务类型对应的处理器
        StateProcessor<StateBaseBean, StateResult> processor = StateProcessorRegistry.getProcessor(opType);
        if (processor == null) {
            throw new IllegalStateException("未找到实体类型[" + opType + "]对应的处理器");
        }
        CommonStateEntity entity = initializeAndGetStateEntity(stateBaseBean, processor);

        // 创建返回结果
        R result = (R) processor.createResultInstance(stateBaseBean);

        // 设置基本状态信息
        result.setState(entity.getState());

        // 设置子状态列表
        List<StateResult.SubState> subStateList = new ArrayList<>();
        Map<Integer, Boolean> subStates = entity.getSubStates();

        if (stateDicList != null) {
            for (StateDicResult.SubStateDic stateDic : stateDicList) {
                StateResult.SubState subState = new StateResult.SubState();
                Integer type = stateDic.getType();
                subState.setType(type);
                subState.setDesc(stateDic.getDesc());
                Boolean value = subStates.get(type);
                subState.setValue(value != null ? value : true );
                subStateList.add(subState);
            }
        }

        result.setSubStateList(subStateList);

        return result;
    }

    private <T extends StateBaseBean, E extends CommonStateEntity> CommonStateEntity initializeAndGetStateEntity(T stateBaseBean, StateProcessor<T, ?> processor) {
        CommonStateEntity entity;
        Criteria criteria = Criteria.where(CommonStateConstant.BUSINESS_TYPE_FIELD).is(stateBaseBean.getBiz());
        processor.generateCriteria(criteria, stateBaseBean);
        entity = commonStateDao.filter(criteria).fetchOne();
        if (entity == null) {
            try {
                E newEntity = (E) processor.buildCommonStateEntity(stateBaseBean);
                newEntity.setBiz(stateBaseBean.getBiz());
                newEntity.setState(Boolean.TRUE);
                newEntity.setSubStates(null);
                commonStateDao.insert(newEntity);
            } catch (DuplicateKeyException e) {
                LOGGER.info("DuplicateKeyException");
            }
            entity = commonStateDao.filter(criteria).fetchOne();
        }

        return entity;
    }
}
