package com.wosai.pay.common.state.manage.service.processor;

import com.wosai.pay.common.state.manage.request.StateBaseBean;
import com.wosai.pay.common.state.manage.result.StateResult;


import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.logging.Logger;

/**
 * 状态处理器注册表
 * 用于注册和管理不同实体类型的处理器
 */
public class StateProcessorRegistry {
    
    private static final Logger LOGGER = Logger.getLogger(StateProcessorRegistry.class.getName());

    private static final Map<String, StateProcessor<?, ?>> PROCESSORS = new HashMap<>();
    
    /**
     * 注册状态处理器
     * @param processor 状态处理器
     * @return 如果注册成功则返回true，如果处理器为null或实体类型为空则返回false
     */
    public static boolean register(StateProcessor<?, ?> processor) {
        if (processor == null) {
            LOGGER.warning("尝试注册null处理器");
            return false;
        }

        String entityType = processor.getSupportedEntityType();
        if (entityType == null || entityType.isEmpty()) {
            LOGGER.warning("处理器返回的实体类型为null或空");
            return false;
        }
        
        if (PROCESSORS.containsKey(entityType)) {
            LOGGER.warning("实体类型 [" + entityType + "] 的处理器已存在，将被覆盖");
        }
        
        PROCESSORS.put(entityType, processor);
        LOGGER.info("已注册实体类型 [" + entityType + "] 的处理器: " + processor.getClass().getName());
        return true;
    }

    /**
     * 获取指定实体类型的处理器
     * @param bizType 实体类型
     * @return 状态处理器，如果未找到则返回null
     */
    @SuppressWarnings("unchecked")
    public static <T extends StateBaseBean,  R extends StateResult>
            StateProcessor<T, R> getProcessor(String bizType) {
        if (bizType == null || bizType.isEmpty()) {
            LOGGER.warning("尝试获取null或空实体类型的处理器");
            return null;
        }
        
        StateProcessor<T, R> processor = (StateProcessor<T, R>) PROCESSORS.get(bizType);
        if (processor == null) {
            LOGGER.warning("未找到实体类型 [" + bizType + "] 的处理器");
        }
        return processor;
    }
    
    /**
     * 获取所有已注册的实体类型
     * @return 实体类型集合
     */
    public static Set<String> getSupportedBizTypes() {
        return PROCESSORS.keySet();
    }
    
    /**
     * 移除指定实体类型的处理器
     * @param bizType 实体类型
     */
    public static void unregister(String bizType) {
        PROCESSORS.remove(bizType);
    }
    
    /**
     * 清除所有已注册的处理器
     */
    public static void clear() {
        PROCESSORS.clear();
    }
}