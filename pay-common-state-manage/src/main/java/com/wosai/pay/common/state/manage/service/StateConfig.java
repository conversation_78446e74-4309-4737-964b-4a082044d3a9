package com.wosai.pay.common.state.manage.service;

import com.google.common.collect.Maps;
import com.wosai.pay.common.data.Criteria;
import com.wosai.pay.common.state.manage.dao.BizDisableReasonDao;
import com.wosai.pay.common.state.manage.dao.entity.BizDisableReasonEntity;
import com.wosai.pay.common.state.manage.exception.StateManageBizException;
import com.wosai.pay.common.state.manage.result.StateDicResult;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.TimeUnit;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * 状态配置管理类
 * 负责加载和管理业务状态配置，支持定时刷新
 */
public class StateConfig {
    
    private static final Logger LOGGER = Logger.getLogger(StateConfig.class.getName());
    
    private final BizDisableReasonDao bizDisableReasonDao;
    
    /**
     * 定时任务执行器
     */
    private ScheduledExecutorService scheduler;
    
    /**
     * 配置刷新间隔（默认5分钟）
     */
    private long refreshIntervalSeconds = 300;
    
    /**
     * 是否启用定时刷新
     */
    private boolean enableAutoRefresh = false;

    /**
     * 构造函数，注入DAO
     *
     * @param bizDisableReasonDao 业务禁用原因DAO
     */
    public StateConfig(BizDisableReasonDao bizDisableReasonDao) {
        this.bizDisableReasonDao = bizDisableReasonDao;
        init();
    }
    
    /**
     * 构造函数，注入DAO并设置刷新间隔
     *
     * @param bizDisableReasonDao 业务禁用原因DAO
     * @param refreshIntervalSeconds 刷新间隔（秒）
     * @param enableAutoRefresh 是否启用定时刷新
     */
    public StateConfig(BizDisableReasonDao bizDisableReasonDao, long refreshIntervalSeconds, boolean enableAutoRefresh) {
        this.bizDisableReasonDao = bizDisableReasonDao;
        this.refreshIntervalSeconds = refreshIntervalSeconds;
        this.enableAutoRefresh = enableAutoRefresh;
        init();
    }

    @PostConstruct
    private void init() {
        // 首次加载配置
        load();
        
        // 如果启用了自动刷新，则启动定时任务
        if (enableAutoRefresh) {
            startScheduledRefresh();
        }
    }
    
    /**
     * 启动定时刷新任务
     */
    public void startScheduledRefresh() {
        // 停止之前的任务（如果有）
        stopScheduledRefresh();
        
        // 创建新的定时任务
        ThreadFactory threadFactory = r -> {
            Thread thread = new Thread(r);
            thread.setName("state-config-refresh");
            thread.setDaemon(true); // 设为守护线程，不阻止JVM退出
            thread.setUncaughtExceptionHandler((t, e) ->
                LOGGER.log(Level.SEVERE, "未捕获的异常在状态配置刷新线程: " + t.getName(), e));
            return thread;
        };
        
        scheduler = Executors.newSingleThreadScheduledExecutor(threadFactory);
        
        // 安排定时任务，定期刷新配置
        scheduler.scheduleAtFixedRate(
            () -> {
                try {
                    LOGGER.info("开始刷新状态配置...");
                    long startTime = System.currentTimeMillis();
                    reload();
                    long endTime = System.currentTimeMillis();
                    LOGGER.info("状态配置刷新完成，耗时: " + (endTime - startTime) + "ms");
                } catch (Exception e) {
                    // 记录错误但不中断调度
                    LOGGER.log(Level.SEVERE, "刷新状态配置失败", e);
                }
            },
            refreshIntervalSeconds, // 初始延迟
            refreshIntervalSeconds, // 间隔
            TimeUnit.SECONDS
        );
        
        LOGGER.info("状态配置定时刷新已启动，间隔: " + refreshIntervalSeconds + "秒");
    }
    
    /**
     * 停止定时刷新任务
     */
    public void stopScheduledRefresh() {
        if (scheduler != null && !scheduler.isShutdown()) {
            LOGGER.info("正在停止状态配置定时刷新任务...");
            scheduler.shutdown();
            try {
                // 等待任务结束
                if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                    scheduler.shutdownNow();
                    LOGGER.warning("状态配置定时刷新任务未能在5秒内正常停止，已强制终止");
                } else {
                    LOGGER.info("状态配置定时刷新任务已正常停止");
                }
            } catch (InterruptedException e) {
                scheduler.shutdownNow();
                Thread.currentThread().interrupt();
                LOGGER.log(Level.WARNING, "停止状态配置定时刷新任务时被中断", e);
            }
            scheduler = null;
        }
    }
    
    /**
     * 设置配置刷新间隔
     *
     * @param refreshIntervalSeconds 刷新间隔（秒）
     */
    public void setRefreshInterval(long refreshIntervalSeconds) {
        if (refreshIntervalSeconds <= 0) {
            throw new IllegalArgumentException("刷新间隔必须大于0秒");
        }
        
        LOGGER.info("更新状态配置刷新间隔: " + this.refreshIntervalSeconds + "秒 -> " + refreshIntervalSeconds + "秒");
        this.refreshIntervalSeconds = refreshIntervalSeconds;
        
        // 如果定时任务已经在运行，则重启以应用新的间隔
        if (enableAutoRefresh && scheduler != null) {
            LOGGER.info("重启定时刷新任务以应用新的刷新间隔");
            startScheduledRefresh();
        }
    }
    
    /**
     * 启用或禁用自动刷新
     *
     * @param enable 是否启用
     */
    public void setEnableAutoRefresh(boolean enable) {
        if (this.enableAutoRefresh == enable) {
            return; // 状态未变，无需操作
        }
        
        LOGGER.info("更改状态配置自动刷新设置: " + (enable ? "启用" : "禁用"));
        this.enableAutoRefresh = enable;
        
        if (enable) {
            startScheduledRefresh();
        } else {
            stopScheduledRefresh();
        }
    }

    private static volatile List<StateDicResult> dicResultList = new ArrayList<>();

    private static volatile Map<String, List<Integer>> bizIdMap = Maps.newHashMap();

    private static volatile Map<String, List<StateDicResult.SubStateDic>> bizMap = Maps.newHashMap();

    /**
     * 验证业务类型和子状态是否有效
     *
     * @param businessType       业务类型
     * @param subStateType  子状态类型
     */
    public static void validateBusinessTypeAndSubState(String businessType, Integer subStateType) {
        if (bizIdMap == null || bizIdMap.isEmpty()) {
            throw new StateManageBizException("状态字典未初始化");
        }

        List<Integer> supportedTypes = bizIdMap.get(businessType);
        if (supportedTypes == null) {
            throw new StateManageBizException("业务类型不存在");
        }
        if (subStateType != null && (!supportedTypes.contains(subStateType))) {
            throw new StateManageBizException("当前业务类型禁用原因未注册");
        }
    }

    /**
     * 加载状态配置
     */
    private void load() {
        try {
            LOGGER.fine("开始加载状态配置...");
            // 从数据库加载所有业务禁用原因记录
            List<BizDisableReasonEntity> bizDisableReasonEntities = bizDisableReasonDao.filter(new Criteria()).fetchAll();
            
            LOGGER.fine("从数据库加载了 " + bizDisableReasonEntities.size() + " 条禁用原因记录");

            // 将记录按业务类型分组
            Map<String, List<BizDisableReasonEntity>> bizGroupMap = bizDisableReasonEntities.stream()
                    .collect(Collectors.groupingBy(BizDisableReasonEntity::getBiz));
            
            // 清空旧数据
            dicResultList = new ArrayList<>();
            bizIdMap = Maps.newHashMap();
            bizMap = Maps.newHashMap();
            
            // 构建业务字典
            for (Map.Entry<String, List<BizDisableReasonEntity>> entry : bizGroupMap.entrySet()) {
                String biz = entry.getKey();
                List<BizDisableReasonEntity> entities = entry.getValue();
                
                // 构建子状态字典列表
                List<StateDicResult.SubStateDic> subStateDics = entities.stream()
                        .map(entity -> new StateDicResult.SubStateDic(
                                entity.getType(),
                                entity.getDesc()))
                        .collect(Collectors.toList());
                
                // 构建状态字典结果
                StateDicResult dicResult = new StateDicResult(
                        biz,
                        biz + "禁用原因",  // 可根据需要调整描述生成逻辑
                        subStateDics
                );
                
                dicResultList.add(dicResult);
                
                // 构建业务ID映射
                bizIdMap.put(biz, subStateDics.stream()
                        .map(StateDicResult.SubStateDic::getType)
                        .collect(Collectors.toList()));
                
                // 构建业务映射
                bizMap.put(biz, subStateDics);
                
                LOGGER.fine("已加载业务 [" + biz + "] 的 " + subStateDics.size() + " 个子状态");
            }
            
            LOGGER.info("状态配置加载完成，共加载了 " + bizIdMap.size() + " 种业务类型");
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "状态配置初始化失败", e);
            throw new StateManageBizException("状态配置初始化失败", e);
        }
    }

    /**
     * 手动重新加载配置
     */
    public synchronized void reload() {
        load();
        LOGGER.info("状态配置已重新加载，时间: " + System.currentTimeMillis());
    }

    /**
     * 获取指定业务类型的子状态列表
     *
     * @param businessType 业务类型
     * @return 子状态列表
     */
    public static List<StateDicResult.SubStateDic> getSubStateList(String businessType) {
        return bizMap.get(businessType);
    }

    /**
     * 获取所有状态字典结果
     *
     * @return 状态字典结果列表
     */
    public static List<StateDicResult> getAllStateDicResults() {
        return dicResultList;
    }

    /**
     * 判断业务类型是否存在
     *
     * @param businessType 业务类型
     * @return 是否存在
     */
    public static boolean isBusinessTypeSupported(String businessType) {
        return bizIdMap.containsKey(businessType);
    }
    
    /**
     * 获取当前刷新间隔（秒）
     */
    public long getRefreshIntervalSeconds() {
        return refreshIntervalSeconds;
    }
    
    /**
     * 检查是否启用了自动刷新
     */
    public boolean isEnableAutoRefresh() {
        return enableAutoRefresh;
    }
    
    /**
     * 清理资源
     * 应在应用关闭时调用此方法
     */
    @PreDestroy
    public void destroy() {
        stopScheduledRefresh();
    }
}
