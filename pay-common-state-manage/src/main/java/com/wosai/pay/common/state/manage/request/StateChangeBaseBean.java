package com.wosai.pay.common.state.manage.request;

public abstract class StateChangeBaseBean extends StateBaseBean {

    /**
     * 状态类型
     */
    private Integer type;

    /**
     * 是否开启（true: 开启, false: 关闭）
     */
    private Boolean open;

    /**
     * 操作备注
     */
    private String remark;

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Boolean getOpen() {
        return open;
    }

    public void setOpen(Boolean open) {
        this.open = open;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
