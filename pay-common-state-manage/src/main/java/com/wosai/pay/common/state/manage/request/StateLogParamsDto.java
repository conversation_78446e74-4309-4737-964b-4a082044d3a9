package com.wosai.pay.common.state.manage.request;


public class StateLogParamsDto {

    /**
     * 平台编码
     */
    private String platformCode;

    /**
     * 操作用户ID
     */
    private String operatorUserId;

    /**
     * 操作用户名称
     */
    private String operatorUserName;

    /**
     * 操作备注
     */
    private String remark;

    /**
     * 外部场景追踪ID
     */
    private String outerSceneTraceId;

    /**
     * 场景模板编码
     */
    private String sceneTemplateCode;

    public String getPlatformCode() {
        return platformCode;
    }

    public void setPlatformCode(String platformCode) {
        this.platformCode = platformCode;
    }

    public String getOperatorUserId() {
        return operatorUserId;
    }

    public void setOperatorUserId(String operatorUserId) {
        this.operatorUserId = operatorUserId;
    }

    public String getOperatorUserName() {
        return operatorUserName;
    }

    public void setOperatorUserName(String operatorUserName) {
        this.operatorUserName = operatorUserName;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getOuterSceneTraceId() {
        return outerSceneTraceId;
    }

    public void setOuterSceneTraceId(String outerSceneTraceId) {
        this.outerSceneTraceId = outerSceneTraceId;
    }

    public String getSceneTemplateCode() {
        return sceneTemplateCode;
    }

    public void setSceneTemplateCode(String sceneTemplateCode) {
        this.sceneTemplateCode = sceneTemplateCode;
    }

    public StateLogParamsDto() {
    }

    public StateLogParamsDto(String platformCode, String operatorUserId, String operatorUserName, String remark, String outerSceneTraceId, String sceneTemplateCode) {
        this.platformCode = platformCode;
        this.operatorUserId = operatorUserId;
        this.operatorUserName = operatorUserName;
        this.remark = remark;
        this.outerSceneTraceId = outerSceneTraceId;
        this.sceneTemplateCode = sceneTemplateCode;
    }

    @Override
    public String toString() {
        return "StateLogParamsDto{" +
                "platformCode='" + platformCode + '\'' +
                ", operatorUserId='" + operatorUserId + '\'' +
                ", operatorUserName='" + operatorUserName + '\'' +
                ", remark='" + remark + '\'' +
                ", outerSceneTraceId='" + outerSceneTraceId + '\'' +
                ", sceneTemplateCode='" + sceneTemplateCode + '\'' +
                '}';
    }

}
