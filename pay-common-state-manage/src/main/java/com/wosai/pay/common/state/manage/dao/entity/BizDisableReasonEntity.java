package com.wosai.pay.common.state.manage.dao.entity;

import com.wosai.pay.common.data.VersionedRecord;

public class BizDisableReasonEntity extends VersionedRecord<Long> {
    /**
     * biz
     */
    private String biz;

    /**
     * 子状态类型
     */
    private Integer type;

    /**
     * 子状态描述
     */
    private String desc;


    public String getBiz() {
        return biz;
    }

    public void setBiz(String biz) {
        this.biz = biz;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public BizDisableReasonEntity() {
    }

    public BizDisableReasonEntity(String businessType, Integer disableReasonType, String description) {
        this.biz = businessType;
        this.type = disableReasonType;
        this.desc = description;
    }
}
